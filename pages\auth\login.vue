<script lang="ts" setup>
import type { FormSubmitEvent } from "@nuxt/ui";
import z from "zod";

definePageMeta({
  layout: "auth",
});

useHead({
  title: "Login",
  meta: [
    {
      name: "description",
      content: "Login to your account",
    },
  ],
});

const loginSchema = z.object({
  email: z
    .string()
    .min(1, "Please enter your email or username")
    .refine(
      (value) => {
        if (value.includes("@")) {
          return z.string().email().safeParse(value).success;
        }
        return true;
      },
      { message: "Invalid email or username" },
    ),
  password: z.string().min(8, "Password must be at least 8 characters"),
});
type LoginSchema = z.infer<typeof loginSchema>;

const showPassword = ref(false);
const formState = reactive({
  email: "",
  password: "",
});

const onSubmit = (event: FormSubmitEvent<LoginSchema>) => {
  console.log(event.data);
};
</script>
<template>
  <UCard class="w-full max-w-sm">
    <div class="bg-brand-400 text-primary text-2xl font-bold">Login</div>
    <div class="mb-2 text-sm text-gray-500">
      Don't have an account?
      <span>
        <UButton
          color="neutral"
          variant="link"
          to="/auth/register"
          class="text-primary -ml-3 font-bold"
        >
          Register here
        </UButton>
      </span>
    </div>
    <UForm
      :schema="loginSchema"
      :state="formState"
      class="space-y-6"
      @submit="onSubmit"
    >
      <UFormField name="email">
        <UInput v-model="formState.email" placeholder="Email/ Username" />
      </UFormField>

      <UFormField name="password">
        <UInput
          v-model="formState.password"
          placeholder="Password"
          :type="showPassword ? 'text' : 'password'"
        >
          <template #trailing>
            <UButton
              color="neutral"
              variant="link"
              size="md"
              :icon="showPassword ? 'i-lucide-eye-off' : 'i-lucide-eye'"
              :aria-label="showPassword ? 'Hide password' : 'Show password'"
              :aria-pressed="showPassword"
              aria-controls="password"
              @click="showPassword = !showPassword"
            />
          </template>
        </UInput>
      </UFormField>
      <UButton type="submit" class="flex w-full justify-center text-white">
        Continue
      </UButton>
    </UForm>
  </UCard>
</template>
