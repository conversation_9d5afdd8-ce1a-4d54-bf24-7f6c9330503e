<script lang="ts" setup>
import type { FormSubmitEvent } from "@nuxt/ui";
import z from "zod";

definePageMeta({
  layout: "auth",
});

useHead({
  title: "Register",
  meta: [
    {
      name: "description",
      content: "Create a new account",
    },
  ],
});

const registerSchema = z
  .object({
    username: z
      .string()
      .min(1, "Please enter your username")
      .min(3, "Username must be at least 3 characters"),
    fullname: z
      .string()
      .min(1, "Please enter your fullname")
      .min(3, "Fullname must be at least 3 characters"),
    email: z.string().min(1, "Please enter your email").email(),
    password: z
      .string()
      .min(1, "Please enter your password")
      .min(8, "Password must be at least 8 characters"),
    confirmPassword: z
      .string()
      .min(1, "Please confirm your password")
      .min(8, "Password must be at least 8 characters"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"], // path of error
  });

type RegisterSchema = z.infer<typeof registerSchema>;

const showPassword = ref(false);
const showConfirmPassword = ref(false);
const formState = reactive({
  username: "",
  fullname: "",
  email: "",
  password: "",
  confirmPassword: "",
});

const onSubmit = (event: FormSubmitEvent<RegisterSchema>) => {
  console.log(event.data);
};
</script>

<template>
  <UCard class="w-full max-w-sm">
    <div class="bg-brand-400 text-primary text-2xl font-bold">
      Create Account
    </div>
    <div class="mb-2 text-sm text-gray-500">
      Have an account?
      <span>
        <UButton
          color="neutral"
          variant="link"
          to="/auth/login"
          class="text-primary -ml-3 font-bold"
        >
          Login here
        </UButton>
      </span>
    </div>
    <UForm
      :schema="registerSchema"
      :state="formState"
      class="space-y-6"
      @submit="onSubmit"
    >
      <UFormField name="fullname">
        <UInput v-model="formState.fullname" placeholder="Fullname" />
      </UFormField>
      <UFormField name="username">
        <UInput v-model="formState.username" placeholder="Username" />
      </UFormField>
      <UFormField name="email">
        <UInput v-model="formState.email" placeholder="Email" />
      </UFormField>
      <UFormField name="password">
        <UInput
          v-model="formState.password"
          placeholder="Password"
          :type="showPassword ? 'text' : 'password'"
        >
          <template #trailing>
            <UButton
              color="neutral"
              variant="link"
              size="md"
              :icon="showPassword ? 'i-lucide-eye-off' : 'i-lucide-eye'"
              :aria-label="showPassword ? 'Hide password' : 'Show password'"
              :aria-pressed="showPassword"
              aria-controls="password"
              @click="showPassword = !showPassword"
            />
          </template>
        </UInput>
      </UFormField>
      <UFormField name="confirmPassword">
        <UInput
          v-model="formState.confirmPassword"
          placeholder="Password Confirmation"
          :type="showConfirmPassword ? 'text' : 'password'"
        >
          <template #trailing>
            <UButton
              color="neutral"
              variant="link"
              size="md"
              :icon="showConfirmPassword ? 'i-lucide-eye-off' : 'i-lucide-eye'"
              :aria-label="
                showConfirmPassword ? 'Hide password' : 'Show password'
              "
              :aria-pressed="showConfirmPassword"
              aria-controls="password"
              @click="showConfirmPassword = !showConfirmPassword"
            />
          </template>
        </UInput>
      </UFormField>
      <UButton type="submit" class="flex w-full justify-center text-white">
        Register
      </UButton>
    </UForm>
  </UCard>
</template>
