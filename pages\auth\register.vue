<script lang="ts" setup>
import type { FormSubmitEvent } from "@nuxt/ui";
import { registerSchema, type RegisterFormData } from "~/composables/useAuth";

definePageMeta({
  layout: "auth",
});

useHead({
  title: "Register",
  meta: [
    {
      name: "description",
      content: "Create a new account",
    },
  ],
});

// Initialize the registration composable
const { useRegister } = useAuth();
const {
  isLoading,
  error,
  fieldErrors,
  isSuccess,
  successMessage,
  register,
  clearState,
} = useRegister();

// Form state
const showPassword = ref(false);
const showConfirmPassword = ref(false);
const formState = reactive<RegisterFormData>({
  username: "",
  fullname: "",
  email: "",
  password: "",
  confirmPassword: "",
});

// Handle form submission
const onSubmit = async (event: FormSubmitEvent<RegisterFormData>) => {
  const result = await register(event.data);

  if (result?.success) {
    // Optional: Redirect to login page after successful registration
    // await navigateTo('/auth/login');

    // Or show success message and allow user to navigate manually
    console.log("Registration successful:", result);
  }
};

// Clear state when component unmounts
onUnmounted(() => {
  clearState();
});
</script>

<template>
  <UCard class="w-full max-w-sm">
    <div class="bg-brand-400 text-primary text-2xl font-bold">
      Create Account
    </div>
    <div class="mb-2 text-sm text-gray-500">
      Have an account?
      <span>
        <UButton
          color="neutral"
          variant="link"
          to="/auth/login"
          class="text-primary -ml-3 font-bold"
        >
          Login here
        </UButton>
      </span>
    </div>

    <!-- Success Message -->
    <UAlert
      v-if="isSuccess && successMessage"
      color="success"
      variant="soft"
      :title="successMessage"
      class="mb-4"
    />

    <!-- General Error Message -->
    <UAlert
      v-if="error && !isSuccess"
      color="error"
      variant="soft"
      :title="error"
      class="mb-4"
    />

    <UForm
      :schema="registerSchema"
      :state="formState"
      class="space-y-6"
      @submit="onSubmit"
    >
      <UFormField name="fullname" :error="fieldErrors.fullname">
        <UInput
          v-model="formState.fullname"
          placeholder="Fullname"
          :disabled="isLoading"
        />
      </UFormField>

      <UFormField name="username" :error="fieldErrors.username">
        <UInput
          v-model="formState.username"
          placeholder="Username"
          :disabled="isLoading"
        />
      </UFormField>

      <UFormField name="email" :error="fieldErrors.email">
        <UInput
          v-model="formState.email"
          placeholder="Email"
          type="email"
          :disabled="isLoading"
        />
      </UFormField>

      <UFormField name="password" :error="fieldErrors.password">
        <UInput
          v-model="formState.password"
          placeholder="Password"
          :type="showPassword ? 'text' : 'password'"
          :disabled="isLoading"
        >
          <template #trailing>
            <UButton
              color="neutral"
              variant="link"
              size="md"
              :icon="showPassword ? 'i-lucide-eye-off' : 'i-lucide-eye'"
              :aria-label="showPassword ? 'Hide password' : 'Show password'"
              :aria-pressed="showPassword"
              aria-controls="password"
              :disabled="isLoading"
              @click="showPassword = !showPassword"
            />
          </template>
        </UInput>
      </UFormField>

      <UFormField name="confirmPassword" :error="fieldErrors.confirmPassword">
        <UInput
          v-model="formState.confirmPassword"
          placeholder="Password Confirmation"
          :type="showConfirmPassword ? 'text' : 'password'"
          :disabled="isLoading"
        >
          <template #trailing>
            <UButton
              color="neutral"
              variant="link"
              size="md"
              :icon="showConfirmPassword ? 'i-lucide-eye-off' : 'i-lucide-eye'"
              :aria-label="
                showConfirmPassword ? 'Hide password' : 'Show password'
              "
              :aria-pressed="showConfirmPassword"
              aria-controls="password"
              :disabled="isLoading"
              @click="showConfirmPassword = !showConfirmPassword"
            />
          </template>
        </UInput>
      </UFormField>

      <UButton
        type="submit"
        class="flex w-full justify-center text-white"
        :loading="isLoading"
        :disabled="isLoading || isSuccess"
      >
        {{ isLoading ? "Creating Account..." : "Register" }}
      </UButton>
    </UForm>

    <!-- Success Actions -->
    <div v-if="isSuccess" class="mt-4 text-center">
      <UButton
        color="neutral"
        variant="link"
        to="/auth/login"
        class="text-primary font-bold"
      >
        Go to Login
      </UButton>
    </div>
  </UCard>
</template>
