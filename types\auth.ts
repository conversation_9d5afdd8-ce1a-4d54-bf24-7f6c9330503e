import { z } from "zod";

/**
 * Authentication-related TypeScript types and validation schemas
 * This file contains all type definitions for authentication functionality
 * including registration, login, and error handling types.
 */

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

/**
 * Registration form validation schema using Zod
 * Validates all registration form fields with comprehensive rules
 */
export const registerSchema = z
  .object({
    username: z
      .string()
      .min(1, "Please enter your username")
      .min(3, "Username must be at least 3 characters")
      .max(50, "Username must be less than 50 characters")
      .regex(
        /^[a-zA-Z0-9_]+$/,
        "Username can only contain letters, numbers, and underscores",
      ),
    fullname: z
      .string()
      .min(1, "Please enter your fullname")
      .min(3, "Fullname must be at least 3 characters")
      .max(100, "Fullname must be less than 100 characters"),
    email: z
      .string()
      .min(1, "Please enter your email")
      .email("Please enter a valid email address"),
    password: z
      .string()
      .min(1, "Please enter your password")
      .min(8, "Password must be at least 8 characters")
      .max(128, "Password must be less than 128 characters")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        "Password must contain at least one uppercase letter, one lowercase letter, and one number",
      ),
    confirmPassword: z.string().min(1, "Please confirm your password"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

/**
 * Login form validation schema using Zod
 * Validates login form fields
 */
export const loginSchema = z.object({
  email: z
    .string()
    .min(1, "Please enter your email or username")
    .refine(
      (value) => {
        if (value.includes("@")) {
          return z.string().email().safeParse(value).success;
        }
        return true;
      },
      { message: "Invalid email or username" },
    ),
  password: z.string().min(8, "Password must be at least 8 characters"),
});

// ============================================================================
// FORM DATA TYPES
// ============================================================================

/**
 * Registration form data type inferred from the Zod schema
 */
export type RegisterFormData = z.infer<typeof registerSchema>;

/**
 * Login form data type inferred from the Zod schema
 */
export type LoginFormData = z.infer<typeof loginSchema>;

// ============================================================================
// API REQUEST TYPES
// ============================================================================

/**
 * Registration API request payload
 * Excludes confirmPassword as it's only used for client-side validation
 */
export interface RegisterRequest {
  username: string;
  fullname: string;
  email: string;
  password: string;
}

/**
 * Login API request payload
 */
export interface LoginRequest {
  email: string;
  password: string;
}

/**
 * Password reset request payload
 */
export interface PasswordResetRequest {
  email: string;
}

/**
 * Password change request payload
 */
export interface PasswordChangeRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

/**
 * User data structure returned from API
 */
export interface User {
  id: string;
  username: string;
  fullname: string;
  email: string;
  emailVerified: boolean;
  avatar?: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Registration API response
 */
export interface RegisterResponse {
  success: boolean;
  message: string;
  data?: {
    id: string;
    username: string;
    fullname: string;
    email: string;
    createdAt: string;
  };
}

/**
 * Login API response
 */
export interface LoginResponse {
  success: boolean;
  message: string;
  data?: {
    user: User;
    token: string;
    refreshToken?: string;
    expiresIn: number;
  };
}

/**
 * Password reset API response
 */
export interface PasswordResetResponse {
  success: boolean;
  message: string;
}

/**
 * Generic authentication API response
 */
export interface AuthResponse {
  success: boolean;
  message: string;
  data?: any;
}

// ============================================================================
// ERROR TYPES
// ============================================================================

/**
 * Authentication error structure
 */
export interface AuthError {
  message: string;
  field?: string;
  code?: string;
}

/**
 * Validation error structure for form fields
 */
export interface ValidationError {
  field: string;
  message: string;
}

/**
 * API error response structure
 */
export interface ApiErrorResponse {
  success: false;
  message: string;
  errors?: Record<string, string | string[]>;
  code?: string;
}

// ============================================================================
// STATE TYPES
// ============================================================================

/**
 * Authentication state structure
 */
export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

/**
 * Registration composable state
 */
export interface RegisterState {
  isLoading: boolean;
  error: string | null;
  fieldErrors: Record<string, string>;
  isSuccess: boolean;
  successMessage: string | null;
}

/**
 * Login composable state
 */
export interface LoginState {
  isLoading: boolean;
  error: string | null;
  fieldErrors: Record<string, string>;
  isSuccess: boolean;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Authentication method types
 */
export type AuthMethod = 'email' | 'username' | 'google' | 'github';

/**
 * User role types
 */
export type UserRole = 'user' | 'admin' | 'moderator';

/**
 * Authentication status types
 */
export type AuthStatus = 'authenticated' | 'unauthenticated' | 'loading' | 'error';
