import { z } from "zod";
import { registerSchema } from "~/types/auth";
import type {
  RegisterFormData,
  RegisterRequest,
  RegisterResponse,
} from "~/types/auth";

export const useAuth = () => {
  const { http } = useApi();

  /**
   * Registration composable function
   * Handles user registration with validation, loading states, and error handling
   */
  const useRegister = () => {
    // Reactive state
    const isLoading = ref(false);
    const error = ref<string | null>(null);
    const fieldErrors = ref<Record<string, string>>({});
    const isSuccess = ref(false);
    const successMessage = ref<string | null>(null);

    /**
     * Clear all error states
     */
    const clearErrors = () => {
      error.value = null;
      fieldErrors.value = {};
    };

    /**
     * Clear all states (useful for form reset)
     */
    const clearState = () => {
      isLoading.value = false;
      error.value = null;
      fieldErrors.value = {};
      isSuccess.value = false;
      successMessage.value = null;
    };

    /**
     * Validate registration form data
     */
    const validateForm = (data: RegisterFormData): boolean => {
      try {
        registerSchema.parse(data);
        clearErrors();
        return true;
      } catch (validationError) {
        if (validationError instanceof z.ZodError) {
          const errors: Record<string, string> = {};
          validationError.errors.forEach((err) => {
            if (err.path.length > 0) {
              errors[err.path[0] as string] = err.message;
            }
          });
          fieldErrors.value = errors;
          error.value = "Please fix the validation errors below";
        }
        return false;
      }
    };

    /**
     * Register a new user
     */
    const register = async (
      formData: RegisterFormData,
    ): Promise<RegisterResponse | null> => {
      try {
        // Clear previous states
        clearErrors();
        isLoading.value = true;
        isSuccess.value = false;

        // Validate form data
        if (!validateForm(formData)) {
          return null;
        }

        // Prepare API request data (exclude confirmPassword)
        const requestData: RegisterRequest = {
          username: formData.username.trim(),
          fullname: formData.fullname.trim(),
          email: formData.email.trim().toLowerCase(),
          password: formData.password,
        };

        // Make API call
        const response = await http.post<RegisterResponse>(
          "/auth/register",
          requestData,
        );

        // Handle successful registration
        isSuccess.value = true;
        successMessage.value =
          response.message ||
          "Registration successful! Please check your email to verify your account.";

        return response;
      } catch (apiError: any) {
        // Handle API errors
        console.error("Registration error:", apiError);

        // Check if it's a validation error from the server
        if (
          apiError.response?.status === 400 &&
          apiError.response?.data?.errors
        ) {
          const serverErrors = apiError.response.data.errors;
          const errors: Record<string, string> = {};

          // Map server validation errors to form fields
          Object.keys(serverErrors).forEach((field) => {
            errors[field] = Array.isArray(serverErrors[field])
              ? serverErrors[field][0]
              : serverErrors[field];
          });

          fieldErrors.value = errors;
          error.value = "Please fix the errors below";
        } else if (apiError.response?.status === 409) {
          // Handle conflict errors (e.g., email/username already exists)
          const conflictMessage =
            apiError.response?.data?.message ||
            "Email or username already exists";
          error.value = conflictMessage;

          // Try to map to specific field if possible
          if (conflictMessage.toLowerCase().includes("email")) {
            fieldErrors.value = { email: conflictMessage };
          } else if (conflictMessage.toLowerCase().includes("username")) {
            fieldErrors.value = { username: conflictMessage };
          }
        } else {
          // Handle other errors
          error.value =
            apiError.message || "Registration failed. Please try again.";
        }

        return null;
      } finally {
        isLoading.value = false;
      }
    };

    return {
      // State
      isLoading: readonly(isLoading),
      error: readonly(error),
      fieldErrors: readonly(fieldErrors),
      isSuccess: readonly(isSuccess),
      successMessage: readonly(successMessage),

      // Methods
      register,
      validateForm,
      clearErrors,
      clearState,
    };
  };

  return {
    useRegister,
  };
};
