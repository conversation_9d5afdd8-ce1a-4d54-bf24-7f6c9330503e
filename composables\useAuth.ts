import { z } from "zod";

// Registration form validation schema
export const registerSchema = z
  .object({
    username: z
      .string()
      .min(1, "Please enter your username")
      .min(3, "Username must be at least 3 characters")
      .max(50, "Username must be less than 50 characters")
      .regex(
        /^[a-zA-Z0-9_]+$/,
        "Username can only contain letters, numbers, and underscores",
      ),
    fullname: z
      .string()
      .min(1, "Please enter your fullname")
      .min(3, "Fullname must be at least 3 characters")
      .max(100, "Fullname must be less than 100 characters"),
    email: z
      .string()
      .min(1, "Please enter your email")
      .email("Please enter a valid email address"),
    password: z
      .string()
      .min(1, "Please enter your password")
      .min(8, "Password must be at least 8 characters")
      .max(128, "Password must be less than 128 characters")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        "Password must contain at least one uppercase letter, one lowercase letter, and one number",
      ),
    confirmPassword: z.string().min(1, "Please confirm your password"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

export type RegisterFormData = z.infer<typeof registerSchema>;

// API request/response types
export interface RegisterRequest {
  username: string;
  fullname: string;
  email: string;
  password: string;
}

export interface RegisterResponse {
  success: boolean;
  message: string;
  data?: {
    id: string;
    username: string;
    fullname: string;
    email: string;
    createdAt: string;
  };
}

export interface AuthError {
  message: string;
  field?: string;
  code?: string;
}

export const useAuth = () => {
  const { http } = useApi();

  /**
   * Registration composable function
   * Handles user registration with validation, loading states, and error handling
   */
  const useRegister = () => {
    // Reactive state
    const isLoading = ref(false);
    const error = ref<string | null>(null);
    const fieldErrors = ref<Record<string, string>>({});
    const isSuccess = ref(false);
    const successMessage = ref<string | null>(null);

    /**
     * Clear all error states
     */
    const clearErrors = () => {
      error.value = null;
      fieldErrors.value = {};
    };

    /**
     * Clear all states (useful for form reset)
     */
    const clearState = () => {
      isLoading.value = false;
      error.value = null;
      fieldErrors.value = {};
      isSuccess.value = false;
      successMessage.value = null;
    };

    /**
     * Validate registration form data
     */
    const validateForm = (data: RegisterFormData): boolean => {
      try {
        registerSchema.parse(data);
        clearErrors();
        return true;
      } catch (validationError) {
        if (validationError instanceof z.ZodError) {
          const errors: Record<string, string> = {};
          validationError.errors.forEach((err) => {
            if (err.path.length > 0) {
              errors[err.path[0] as string] = err.message;
            }
          });
          fieldErrors.value = errors;
          error.value = "Please fix the validation errors below";
        }
        return false;
      }
    };

    /**
     * Register a new user
     */
    const register = async (
      formData: RegisterFormData,
    ): Promise<RegisterResponse | null> => {
      try {
        // Clear previous states
        clearErrors();
        isLoading.value = true;
        isSuccess.value = false;

        // Validate form data
        if (!validateForm(formData)) {
          return null;
        }

        // Prepare API request data (exclude confirmPassword)
        const requestData: RegisterRequest = {
          username: formData.username.trim(),
          fullname: formData.fullname.trim(),
          email: formData.email.trim().toLowerCase(),
          password: formData.password,
        };

        // Make API call
        const response = await http.post<RegisterResponse>(
          "/auth/register",
          requestData,
        );

        // Handle successful registration
        isSuccess.value = true;
        successMessage.value =
          response.message ||
          "Registration successful! Please check your email to verify your account.";

        return response;
      } catch (apiError: any) {
        // Handle API errors
        console.error("Registration error:", apiError);

        // Check if it's a validation error from the server
        if (
          apiError.response?.status === 400 &&
          apiError.response?.data?.errors
        ) {
          const serverErrors = apiError.response.data.errors;
          const errors: Record<string, string> = {};

          // Map server validation errors to form fields
          Object.keys(serverErrors).forEach((field) => {
            errors[field] = Array.isArray(serverErrors[field])
              ? serverErrors[field][0]
              : serverErrors[field];
          });

          fieldErrors.value = errors;
          error.value = "Please fix the errors below";
        } else if (apiError.response?.status === 409) {
          // Handle conflict errors (e.g., email/username already exists)
          const conflictMessage =
            apiError.response?.data?.message ||
            "Email or username already exists";
          error.value = conflictMessage;

          // Try to map to specific field if possible
          if (conflictMessage.toLowerCase().includes("email")) {
            fieldErrors.value = { email: conflictMessage };
          } else if (conflictMessage.toLowerCase().includes("username")) {
            fieldErrors.value = { username: conflictMessage };
          }
        } else {
          // Handle other errors
          error.value =
            apiError.message || "Registration failed. Please try again.";
        }

        return null;
      } finally {
        isLoading.value = false;
      }
    };

    return {
      // State
      isLoading: readonly(isLoading),
      error: readonly(error),
      fieldErrors: readonly(fieldErrors),
      isSuccess: readonly(isSuccess),
      successMessage: readonly(successMessage),

      // Methods
      register,
      validateForm,
      clearErrors,
      clearState,
    };
  };

  return {
    useRegister,
  };
};
